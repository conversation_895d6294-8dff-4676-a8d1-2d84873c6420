"""
私域网站爬虫
"""

# 配置选项
HEADLESS_MODE = True  # True=无头模式(后台运行), False=窗口模式(显示浏览器)

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import time
import requests
import os
import base64
from PIL import Image, ImageEnhance, ImageFilter
import io
import cv2
import numpy as np
import logging

# 验证码识别相关导入
DDDDOCR_AVAILABLE = False
PYTESSERACT_AVAILABLE = False

# 修补Pillow兼容性问题
def patch_pillow_compatibility():
    """修补Pillow兼容性问题"""
    try:
        from PIL import Image
        if not hasattr(Image, 'ANTIALIAS'):
            Image.ANTIALIAS = Image.LANCZOS
            print("[+] 已修补Pillow兼容性问题")
    except:
        pass

# 尝试导入ddddocr
try:
    patch_pillow_compatibility()  # 先修补兼容性
    import ddddocr
    DDDDOCR_AVAILABLE = True
    print("[+] ddddocr库已加载，将使用ddddocr识别验证码")
except ImportError as e:
    print(f"[-] ddddocr库加载失败: {e}")
except Exception as e:
    print(f"[-] ddddocr初始化失败: {e}")

# 如果ddddocr不可用，尝试pytesseract
if not DDDDOCR_AVAILABLE:
    try:
        import pytesseract
        PYTESSERACT_AVAILABLE = True
        print("[+] pytesseract库已加载，将使用pytesseract识别验证码")
    except ImportError:
        print("[-] pytesseract库未安装，请运行: pip install pytesseract")

if not DDDDOCR_AVAILABLE and not PYTESSERACT_AVAILABLE:
    print("[-] 所有OCR库都不可用，将回退到手动输入验证码模式")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SeleniumLoginSolution:
    def __init__(self, headless=False, ddddocr_enabled=True):
        """
        初始化Selenium浏览器和ddddocr验证码识别器
        Args:
            headless: 是否使用无头模式（True=后台运行，False=显示浏览器窗口）
            ddddocr_enabled: 是否启用ddddocr自动识别验证码（True=自动识别，False=手动输入）
        """
        self.driver = None
        self.wait = None
        self.headless = headless
        self.ocr_enabled = ddddocr_enabled and (DDDDOCR_AVAILABLE or PYTESSERACT_AVAILABLE)
        self.ocr = None
        self.ocr_type = None

        # 初始化OCR识别器
        if self.ocr_enabled:
            if DDDDOCR_AVAILABLE:
                try:
                    self.ocr = ddddocr.DdddOcr()
                    self.ocr_type = "ddddocr"
                    logging.info("ddddocr验证码识别器初始化成功")
                except Exception as e:
                    logging.error(f"ddddocr识别器初始化失败: {e}")
                    self.ocr_enabled = False
            elif PYTESSERACT_AVAILABLE:
                try:
                    # pytesseract不需要特殊初始化
                    self.ocr_type = "pytesseract"
                    logging.info("pytesseract验证码识别器初始化成功")
                except Exception as e:
                    logging.error(f"pytesseract识别器初始化失败: {e}")
                    self.ocr_enabled = False

        if not self.ocr_enabled:
            logging.warning("OCR验证码识别功能未启用，将使用手动输入验证码模式")

        self.setup_driver()
    
    def setup_driver(self):
        """配置Chrome浏览器"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument('--headless')
            # 无头模式专用优化
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-software-rasterizer')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-features=TranslateUI')
            chrome_options.add_argument('--disable-ipc-flooding-protection')

        # 反检测配置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 性能优化
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        # 注意：不禁用图片和JS，因为验证码需要图片，登录可能需要JS

        # 设置用户代理
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')

        # 窗口大小
        chrome_options.add_argument('--window-size=1920,1080')
        
        try:
            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            mode_text = "无头模式" if self.headless else "窗口模式"
            print(f"[+] Chrome浏览器启动成功 ({mode_text})")
        except Exception as e:
            print(f"[-] 浏览器启动失败: {e}")
            print("请确保已安装Chrome浏览器")
            print("如果问题持续，请运行: python install_dependencies.py")
            raise

    def preprocess_captcha_image(self, image_path):
        """
        验证码图片预处理，提高OCR识别准确率
        Args:
            image_path: 验证码图片路径
        Returns:
            str: 预处理后的图片路径
        """
        try:
            logging.info(f"开始预处理验证码图片: {image_path}")

            # 使用PIL打开图片
            with Image.open(image_path) as img:
                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 调整图片大小，放大以提高识别率
                width, height = img.size
                if width < 200 or height < 80:
                    scale_factor = max(200/width, 80/height)
                    new_width = int(width * scale_factor)
                    new_height = int(height * scale_factor)
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    logging.info(f"图片已放大: {width}x{height} -> {new_width}x{new_height}")

                # 增强对比度
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(2.0)  # 增强对比度

                # 增强锐度
                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(2.0)  # 增强锐度

                # 转换为numpy数组进行OpenCV处理
                img_array = np.array(img)

                # 转换为灰度图
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)

                # 高斯模糊去噪
                blurred = cv2.GaussianBlur(gray, (3, 3), 0)

                # 自适应阈值二值化
                binary = cv2.adaptiveThreshold(
                    blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                    cv2.THRESH_BINARY, 11, 2
                )

                # 形态学操作去除噪点
                kernel = np.ones((2, 2), np.uint8)
                cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
                cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

                # 保存预处理后的图片
                processed_path = image_path.replace('.png', '_processed.png')
                cv2.imwrite(processed_path, cleaned)

                logging.info(f"图片预处理完成: {processed_path}")
                return processed_path

        except Exception as e:
            logging.error(f"图片预处理失败: {e}")
            return image_path  # 返回原图片路径

    def recognize_captcha_with_ocr(self, image_path, max_attempts=3):
        """
        使用OCR识别验证码（支持ddddocr和pytesseract）
        Args:
            image_path: 验证码图片路径
            max_attempts: 最大尝试次数
        Returns:
            str: 识别出的验证码文本，失败返回None
        """
        if not self.ocr_enabled:
            logging.warning("OCR验证码识别功能未启用")
            return None

        try:
            logging.info(f"开始{self.ocr_type}识别验证码: {image_path}")

            best_result = None

            # 尝试多次识别
            for attempt in range(max_attempts):
                try:
                    logging.info(f"{self.ocr_type}识别尝试 {attempt + 1}/{max_attempts}")

                    if self.ocr_type == "ddddocr":
                        result = self._recognize_with_ddddocr(image_path)
                    elif self.ocr_type == "pytesseract":
                        result = self._recognize_with_pytesseract(image_path)
                    else:
                        logging.error(f"未知的OCR类型: {self.ocr_type}")
                        return None

                    if result:
                        # 清理识别结果
                        cleaned_result = self._clean_ocr_result(result)

                        logging.info(f"{self.ocr_type}识别结果 (尝试{attempt+1}): 原始='{result}', 清理后='{cleaned_result}'")

                        # 验证结果是否合理
                        if cleaned_result and 3 <= len(cleaned_result) <= 8:
                            best_result = cleaned_result
                            break

                except Exception as e:
                    logging.error(f"{self.ocr_type}识别尝试{attempt+1}失败: {e}")
                    continue

            if best_result:
                logging.info(f"{self.ocr_type}识别成功: '{best_result}'")
                return best_result
            else:
                logging.warning(f"{self.ocr_type}识别失败")
                return None

        except Exception as e:
            logging.error(f"{self.ocr_type}识别过程出错: {e}")
            return None

    def _recognize_with_ddddocr(self, image_path):
        """使用ddddocr识别"""
        with open(image_path, 'rb') as f:
            img_bytes = f.read()
        return self.ocr.classification(img_bytes)

    def _recognize_with_pytesseract(self, image_path):
        """使用pytesseract识别"""
        # 预处理图片
        processed_image_path = self.preprocess_captcha_image(image_path)

        # 使用pytesseract识别
        from PIL import Image
        import pytesseract

        img = Image.open(processed_image_path)
        # 配置pytesseract参数，只识别字母数字
        config = '--psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
        result = pytesseract.image_to_string(img, config=config).strip()

        # 清理预处理后的图片
        try:
            if processed_image_path != image_path and os.path.exists(processed_image_path):
                os.remove(processed_image_path)
        except:
            pass

        return result

    def _clean_ocr_result(self, text):
        """
        清理OCR识别结果
        Args:
            text: 原始OCR识别文本
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""

        # 移除空格和换行符
        text = text.strip().replace('\n', '').replace('\r', '').replace(' ', '')

        # 只保留字母数字
        cleaned = ''.join(char for char in text if char.isalnum())

        # 常见字符混淆修正
        replacements = {
            'O': '0',  # 字母O替换为数字0
            'o': '0',  # 小写o替换为数字0
            'I': '1',  # 字母I替换为数字1
            'l': '1',  # 小写l替换为数字1
            'S': '5',  # 字母S有时会被误识别
            'G': '6',  # 字母G有时会被误识别
            'B': '8',  # 字母B有时会被误识别
            'Z': '2',  # 字母Z有时会被误识别为2
        }

        # 应用替换规则（仅当结果看起来像验证码时）
        if len(cleaned) >= 3 and cleaned.isalnum():
            for old, new in replacements.items():
                cleaned = cleaned.replace(old, new)

        return cleaned.upper()  # 转换为大写保持一致性


    
    def login(self, username="***********", password="aabb6688"):
        """
        执行自动登录
        Args:
            username: 用户名
            password: 密码
        Returns:
            bool: 登录是否成功
        """
        try:
            print("[+] 开始Selenium自动登录...")
            
            # 1. 访问登录页面
            login_url = "https://login-sso.siyscrm.com/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id=81c4e66b04066aa8da079f3e5d2bf1f3"
            print(f"[+] 访问登录页面: {login_url}")
            self.driver.get(login_url)
            
            # 等待页面加载
            time.sleep(3)

            # 调试：打印页面标题确认页面加载
            print(f"[DEBUG] 页面标题: {self.driver.title}")
            print(f"[DEBUG] 当前URL: {self.driver.current_url}")
            
            # 2. 填写用户名
            print("[+] 填写用户名...")
            username_input = self.wait.until(EC.presence_of_element_located((By.ID, "userAccount")))
            username_input.clear()
            username_input.send_keys(username)
            
            # 3. 填写密码
            print("[+] 填写密码...")
            password_input = self.driver.find_element(By.ID, "pwd")
            password_input.clear()
            password_input.send_keys(password)
            
            # 4. 处理验证码（包含登录按钮点击和验证）
            print("[+] 处理验证码...")
            captcha_success = self.handle_captcha()
            if not captcha_success:
                print("[-] 验证码处理失败")
                return False

            # 5. 等待登录结果（验证码处理成功后）
            print("[+] 验证码验证成功，等待最终登录结果...")
            return self.wait_for_login_result()
            
        except TimeoutException:
            print("[-] 页面加载超时")
            return False
        except NoSuchElementException as e:
            print(f"[-] 页面元素未找到: {e}")
            return False
        except Exception as e:
            print(f"[-] 登录过程出错: {e}")
            return False
    
    def refresh_captcha(self):
        """
        刷新验证码图片
        Returns:
            bool: 刷新是否成功
        """
        try:
            logging.info("尝试刷新验证码...")

            # 方法1: 尝试点击验证码图片刷新
            try:
                captcha_img = self.driver.find_element(By.ID, "mixImg")
                captcha_img.click()
                logging.info("已点击验证码图片进行刷新")

                # 等待新验证码加载完成
                logging.info("等待新验证码加载...")
                time.sleep(3)  # 增加等待时间

                # 验证新验证码是否加载完成
                try:
                    img_loaded = self.driver.execute_script("""
                        var img = document.getElementById('mixImg');
                        return img && img.complete && img.naturalWidth > 0;
                    """)

                    if not img_loaded:
                        logging.warning("新验证码可能未完全加载，额外等待...")
                        time.sleep(2)
                    else:
                        logging.info("新验证码已完全加载")
                except:
                    logging.info("使用固定等待时间确保新验证码加载")
                    time.sleep(1)

                return True
            except Exception as e:
                logging.warning(f"点击验证码图片刷新失败: {e}")

            # 方法2: 尝试查找刷新按钮
            try:
                refresh_selectors = [
                    "button[onclick*='refresh']",
                    "a[onclick*='refresh']",
                    ".refresh-captcha",
                    "#refreshCaptcha",
                    "img[onclick*='refresh']"
                ]

                for selector in refresh_selectors:
                    try:
                        refresh_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                        refresh_btn.click()
                        logging.info(f"已点击刷新按钮: {selector}")
                        time.sleep(3)  # 增加等待时间确保新验证码加载
                        return True
                    except:
                        continue

            except Exception as e:
                logging.warning(f"查找刷新按钮失败: {e}")

            # 方法3: 重新加载页面（最后手段）
            logging.warning("使用页面重新加载来刷新验证码")
            self.driver.refresh()
            time.sleep(5)  # 页面重新加载需要更长时间
            return True

        except Exception as e:
            logging.error(f"刷新验证码失败: {e}")
            return False

    def check_captcha_error(self):
        """
        检查验证码是否错误 - 针对无头模式优化
        Returns:
            bool: True表示验证码错误，需要重试
        """
        try:
            # 等待页面响应
            time.sleep(3)

            current_url = self.driver.current_url
            logging.info(f"检查验证码错误状态，当前URL: {current_url}")

            # 检查方法1: URL变化检测
            # 如果URL已经跳转离开登录页面，说明登录可能成功
            if "login-sso.siyscrm.com" not in current_url:
                logging.info("已离开登录页面，验证码可能正确")
                return False

            if "apollo.siyscrm.com" in current_url and "login" not in current_url.lower():
                logging.info("已跳转到目标域名，验证码正确")
                return False

            # 检查方法2: 查找错误提示文本
            error_texts = [
                "验证码错误", "验证码不正确", "验证码有误",
                "captcha error", "invalid captcha", "wrong captcha",
                "验证码输入错误", "请重新输入验证码", "验证码不能为空"
            ]

            page_text = self.driver.page_source.lower()
            for error_text in error_texts:
                if error_text.lower() in page_text:
                    logging.warning(f"检测到验证码错误提示: {error_text}")
                    return True

            # 检查方法3: 验证码输入框状态
            try:
                captcha_input = self.driver.find_element(By.ID, "pictureVerifVode")
                input_value = captcha_input.get_attribute("value")
                if input_value == "":
                    logging.warning("验证码输入框已清空，可能验证码错误")
                    return True
                else:
                    logging.info(f"验证码输入框仍有值: {input_value}")
            except:
                logging.warning("无法找到验证码输入框")

            # 检查方法4: 页面标题检测
            try:
                page_title = self.driver.title
                if page_title and "登录" in page_title:
                    logging.info(f"仍在登录页面，标题: {page_title}")
                    # 如果5秒后仍在登录页面，可能是验证码错误
                    time.sleep(2)
                    new_url = self.driver.current_url
                    if new_url == current_url and "login" in new_url.lower():
                        logging.warning("页面未发生跳转，可能验证码错误")
                        return True
            except:
                pass

            # 检查方法5: 查找特定的错误元素
            error_selectors = [
                ".error-message", ".alert-danger", ".error-tip",
                "#error-msg", ".captcha-error", "[class*='error']",
                ".alert", ".message", ".warning"
            ]

            for selector in error_selectors:
                try:
                    error_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in error_elements:
                        if element.is_displayed() and element.text.strip():
                            error_text = element.text.strip()
                            if any(keyword in error_text.lower() for keyword in ["验证码", "captcha", "错误", "error", "失败", "fail"]):
                                logging.warning(f"检测到错误元素: {error_text}")
                                return True
                except:
                    continue

            logging.info("未检测到明确的验证码错误，假设验证码正确")
            return False

        except Exception as e:
            logging.error(f"检查验证码错误状态失败: {e}")
            # 出现异常时，保守地认为没有错误
            return False

    def capture_and_recognize_captcha(self, attempt_num=1):
        """
        截取并识别验证码
        Args:
            attempt_num: 尝试次数
        Returns:
            str: 识别出的验证码，失败返回None
        """
        try:
            logging.info(f"第{attempt_num}次尝试截取并识别验证码...")

            # 查找验证码图片
            captcha_img = None
            try:
                captcha_img = self.driver.find_element(By.ID, "mixImg")
                logging.info("找到验证码图片元素: #mixImg")
            except NoSuchElementException:
                logging.error("未找到验证码图片元素")
                return None

            if not captcha_img:
                logging.error("验证码图片元素为空")
                return None

            # 等待验证码图片完全加载
            logging.info("等待验证码图片完全加载...")
            time.sleep(2)  # 给验证码图片更多加载时间

            # 检查图片是否真正加载完成
            try:
                # 通过JavaScript检查图片是否完全加载
                img_loaded = self.driver.execute_script("""
                    var img = document.getElementById('mixImg');
                    return img && img.complete && img.naturalWidth > 0;
                """)

                if not img_loaded:
                    logging.warning("验证码图片可能未完全加载，等待更长时间...")
                    time.sleep(3)  # 额外等待时间
                else:
                    logging.info("验证码图片已完全加载")
            except:
                # 如果JavaScript检查失败，使用固定等待时间
                logging.info("使用固定等待时间确保图片加载")
                time.sleep(2)

            # 截取验证码图片
            captcha_screenshot = captcha_img.screenshot_as_png

            # 保存验证码图片
            captcha_filename = f"captcha_attempt_{attempt_num}_{int(time.time())}.png"
            with open(captcha_filename, 'wb') as f:
                f.write(captcha_screenshot)

            logging.info(f"验证码已保存: {captcha_filename}")

            # 尝试OCR自动识别
            captcha_code = None
            if self.ocr_enabled:
                logging.info(f"尝试使用{self.ocr_type}自动识别验证码...")
                captcha_code = self.recognize_captcha_with_ocr(captcha_filename)

                if captcha_code:
                    logging.info(f"{self.ocr_type}识别成功: {captcha_code}")
                else:
                    logging.warning(f"{self.ocr_type}识别失败，回退到手动输入模式")

            # 如果OCR识别失败或未启用，则手动输入
            if not captcha_code:
                print(f"\n验证码图片已保存: {captcha_filename}")
                if self.headless:
                    print("[!] 无头模式运行中 - 请在文件管理器中查看验证码图片")
                print(f"第{attempt_num}次尝试 - 请查看验证码图片并手动输入:")
                captcha_code = input("请输入验证码: ").strip()

            # 清理验证码文件
            try:
                os.remove(captcha_filename)
                logging.info("已清理验证码文件")
            except Exception as e:
                logging.warning(f"清理验证码文件失败: {e}")

            return captcha_code

        except Exception as e:
            logging.error(f"截取并识别验证码失败: {e}")
            return None

    def handle_captcha(self):
        """
        处理验证码 - 集成重试机制和ddddocr自动识别功能
        Returns:
            bool: 验证码处理是否成功
        """
        max_retries = 3  # 最大重试次数
        total_attempts = max_retries + 1  # 总尝试次数

        try:
            logging.info("开始处理验证码（支持重试机制）...")
            # 等待页面完全加载
            time.sleep(2)

            for attempt in range(1, total_attempts + 1):
                logging.info(f"验证码处理第{attempt}/{total_attempts}次尝试")

                # 如果不是第一次尝试，需要刷新验证码
                if attempt > 1:
                    logging.info(f"第{attempt}次尝试，先刷新验证码...")
                    if not self.refresh_captcha():
                        logging.error("刷新验证码失败")
                        continue

                # 截取并识别验证码
                captcha_code = self.capture_and_recognize_captcha(attempt)

                if not captcha_code:
                    logging.error(f"第{attempt}次尝试：验证码不能为空")
                    if attempt < total_attempts:
                        logging.info("准备进行下一次尝试...")
                        continue
                    else:
                        logging.error("所有尝试都失败，验证码处理失败")
                        return False

                # 查找验证码输入框
                try:
                    captcha_input = self.driver.find_element(By.ID, "pictureVerifVode")
                    logging.info("找到验证码输入框: #pictureVerifVode")
                except NoSuchElementException:
                    logging.error("未找到验证码输入框")
                    if attempt < total_attempts:
                        continue
                    else:
                        return False

                # 填写验证码
                captcha_input.clear()
                captcha_input.send_keys(captcha_code)
                logging.info(f"第{attempt}次尝试：验证码已填入: {captcha_code}")

                # 点击登录按钮进行验证
                try:
                    login_button = self.driver.find_element(By.CSS_SELECTOR, "body > div > div > div > form > div:nth-of-type(1) > div:nth-of-type(6) > button")

                    # 记录点击前的状态
                    before_url = self.driver.current_url
                    logging.info(f"点击登录按钮前URL: {before_url}")

                    login_button.click()
                    logging.info("已点击登录按钮，等待验证结果...")

                    # 等待页面响应
                    time.sleep(3)

                    # 记录点击后的状态
                    after_url = self.driver.current_url
                    logging.info(f"点击登录按钮后URL: {after_url}")

                    # 如果URL发生了变化，可能是登录成功
                    if after_url != before_url:
                        logging.info("URL发生变化，可能登录成功")
                        if "apollo.siyscrm.com" in after_url or "login-sso.siyscrm.com" not in after_url:
                            logging.info(f"第{attempt}次尝试：验证码验证成功！")
                            return True

                    # 检查验证码是否错误
                    if self.check_captcha_error():
                        logging.warning(f"第{attempt}次尝试：验证码错误")
                        if attempt < total_attempts:
                            logging.info("准备重试...")
                            continue
                        else:
                            logging.error("达到最大重试次数，验证码处理失败")
                            return False
                    else:
                        logging.info(f"第{attempt}次尝试：验证码验证成功！")
                        return True

                except Exception as e:
                    logging.error(f"第{attempt}次尝试：点击登录按钮失败: {e}")
                    if attempt < total_attempts:
                        continue
                    else:
                        return False

            logging.error("所有验证码尝试都失败")
            return False

        except Exception as e:
            logging.error(f"验证码处理过程出错: {e}")
            return False
    
    def wait_for_login_result(self, timeout=30):
        """等待登录结果 - 针对无头模式优化"""
        start_time = time.time()
        print(f"[+] 等待登录结果，超时时间: {timeout}秒")

        while time.time() - start_time < timeout:
            current_url = self.driver.current_url
            elapsed_time = int(time.time() - start_time)

            # 每5秒输出一次当前状态
            if elapsed_time % 5 == 0 and elapsed_time > 0:
                print(f"[+] 等待中... ({elapsed_time}s) 当前URL: {current_url}")

            # 检查多种登录成功的条件
            login_success = False

            # 条件1: URL跳转到目标域名
            if "apollo.siyscrm.com" in current_url and "login" not in current_url.lower():
                print(f"[+] 检测到URL跳转: {current_url}")
                login_success = True

            # 条件2: 检查页面标题变化
            try:
                page_title = self.driver.title
                if page_title and "登录" not in page_title and page_title != "登录":
                    print(f"[+] 检测到页面标题变化: {page_title}")
                    login_success = True
            except:
                pass

            # 条件3: 检查是否有登录成功的特征元素
            try:
                # 查找可能的成功标识
                success_indicators = [
                    "dashboard", "main", "home", "index", "welcome",
                    "用户中心", "控制台", "主页", "首页"
                ]
                page_source = self.driver.page_source.lower()
                for indicator in success_indicators:
                    if indicator in page_source:
                        print(f"[+] 检测到成功标识: {indicator}")
                        login_success = True
                        break
            except:
                pass

            # 条件4: 检查是否离开了登录页面
            if "login-sso.siyscrm.com" not in current_url:
                print(f"[+] 已离开登录页面: {current_url}")
                login_success = True

            if login_success:
                print("[+] 登录成功!")
                print(f"[+] 最终URL: {current_url}")

                # 等待页面完全加载
                print("[+] 等待页面完全加载...")
                time.sleep(5)

                # 获取页面标题和内容
                try:
                    page_title = self.driver.title
                    print(f"[+] 页面标题: {page_title}")
                except:
                    print("[!] 无法获取页面标题")

                # 打印页面内容
                self.print_page_content()

                return True

            # 检查是否有错误信息
            try:
                error_selectors = [
                    ".error", ".alert", ".message", ".warning",
                    "[class*='error']", "[class*='alert']", "[class*='fail']"
                ]
                for selector in error_selectors:
                    error_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in error_elements:
                        if element.is_displayed() and element.text.strip():
                            error_text = element.text.strip()
                            if any(keyword in error_text.lower() for keyword in ["错误", "失败", "error", "fail", "invalid"]):
                                print(f"[-] 登录错误: {error_text}")
                                return False
            except:
                pass

            time.sleep(2)  # 增加检查间隔

        print(f"[-] 登录超时 (等待了{timeout}秒)")
        print(f"[-] 最终URL: {self.driver.current_url}")
        return False
    
    def print_page_content(self):
        """打印登录成功后的页面内容"""
        try:
            print("[+] 登录成功后的页面内容:")
            print("=" * 80)
            
            # 获取页面文本内容
            body_text = self.driver.find_element(By.TAG_NAME, "body").text
            print(body_text[:1000])  # 打印前1000个字符
            
            print("=" * 80)
            
            # 获取主要导航菜单
            try:
                nav_elements = self.driver.find_elements(By.CSS_SELECTOR, "nav a, .nav a, .menu a, .sidebar a")
                if nav_elements:
                    print("[+] 主要功能菜单:")
                    for nav in nav_elements[:10]:  # 只显示前10个
                        if nav.text.strip():
                            print(f"  - {nav.text.strip()}")
            except:
                pass
            
        except Exception as e:
            print(f"[-] 获取页面内容失败: {e}")
    
    def get_cookies(self):
        """获取登录后的Cookies"""
        try:
            cookies = self.driver.get_cookies()
            print("[+] 登录Cookies:")
            for cookie in cookies:
                print(f"  {cookie['name']}: {cookie['value']}")
            return cookies
        except Exception as e:
            print(f"[-] 获取Cookies失败: {e}")
            return []
    
    def extract_session_for_requests(self):
        """提取会话信息用于requests库"""
        try:
            cookies = self.driver.get_cookies()
            session = requests.Session()
            
            # 添加cookies到requests session
            for cookie in cookies:
                session.cookies.set(cookie['name'], cookie['value'])
            
            # 添加常用请求头
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            })
            
            print("[+] 会话信息已提取到requests.Session对象")
            return session
            
        except Exception as e:
            print(f"[-] 提取会话信息失败: {e}")
            return None
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("[+] 浏览器已关闭")

def main():
    """主函数"""
    print("=" * 70)
    print("私域网站爬虫 - Selenium自动化解决方案 (集成OCR验证码识别)")
    print("=" * 70)

    # 显示运行模式
    mode_text = "无头模式 (后台运行，不显示浏览器窗口)" if HEADLESS_MODE else "窗口模式 (显示浏览器窗口)"
    print(f"[+] 运行模式: {mode_text}")
    if not HEADLESS_MODE:
        print("[!] 如需后台运行，请将文件顶部的 HEADLESS_MODE 设置为 True")
    print("=" * 70)

    # 检查OCR验证码识别功能状态
    if DDDDOCR_AVAILABLE:
        print("[+] ddddocr验证码识别功能已启用 - 将使用ddddocr自动识别验证码")
    elif PYTESSERACT_AVAILABLE:
        print("[+] pytesseract验证码识别功能已启用 - 将使用pytesseract自动识别验证码")
    else:
        print("[-] OCR验证码识别功能未启用 - 需要手动输入验证码")
        print("   安装依赖: pip install ddddocr 或 pip install pytesseract")

    print()

    # 创建登录实例
    login_bot = SeleniumLoginSolution(
        headless=HEADLESS_MODE,   # 使用配置的运行模式
        ddddocr_enabled=True      # 启用OCR自动识别验证码
    )
    
    try:
        # 执行登录
        success = login_bot.login()
        
        if success:
            print("[+] 登录流程成功完成!")
            
            # 获取Cookies
            login_bot.get_cookies()
            
            # 提取会话信息
            session = login_bot.extract_session_for_requests()
            
            if session:
                print("[+] 现在可以使用session对象进行后续的数据抓取")
                print("示例: response = session.get('https://apollo.siyscrm.com/api/data')")
            
            # 保持浏览器打开一段时间供用户查看
            input("按Enter键关闭浏览器...")
            
        else:
            print("[-] 登录失败")
            
    except KeyboardInterrupt:
        print("\n[!] 用户中断操作")
    except Exception as e:
        print(f"[-] 程序执行出错: {e}")
    finally:
        login_bot.close()

if __name__ == "__main__":
    main()
