# 运行模式说明

## 概述

本项目支持两种运行模式：
- **无头模式**：后台运行，不显示浏览器窗口（默认）
- **窗口模式**：显示浏览器窗口，可以看到操作过程

本项目集成了ddddocr验证码自动识别功能，支持自动处理验证码，无需人工干预。

## 当前配置

默认使用 **无头模式** 运行，适合以下场景：
- 服务器环境部署
- 后台自动化任务
- 不需要观察浏览器操作过程
- 提高运行效率

## 切换运行模式

### 方法1：修改配置文件

编辑 `selenium_login_solution.py` 文件顶部的配置：

```python
# 配置选项
HEADLESS_MODE = True   # True=无头模式(后台运行), False=窗口模式(显示浏览器)
```

- 设置为 `True`：无头模式（默认）
- 设置为 `False`：窗口模式

### 方法2：临时修改

如果只是临时需要查看浏览器操作，可以在创建实例时指定：

```python
# 临时使用窗口模式
login_bot = SeleniumLoginSolution(
    headless=False,       # 显示浏览器窗口
    ddddocr_enabled=True
)
```

## 两种模式的区别

### 无头模式 (headless=True)
**优点：**
- 占用资源更少
- 运行速度更快
- 适合服务器环境
- 不会干扰其他工作

**缺点：**
- 无法直观看到操作过程
- 调试相对困难
- 验证码需要查看保存的图片文件

**适用场景：**
- 生产环境自动化
- 定时任务
- 服务器部署
- 批量处理

### 窗口模式 (headless=False)
**优点：**
- 可以直观看到操作过程
- 便于调试和排错
- 可以手动干预
- 验证码直接在浏览器中显示

**缺点：**
- 占用更多资源
- 会显示浏览器窗口
- 可能影响其他工作

**适用场景：**
- 开发调试
- 首次运行测试
- 排查问题
- 学习了解流程

## 无头模式优化

为了提高无头模式的性能，程序自动启用了以下优化：

```python
# 无头模式专用优化
chrome_options.add_argument('--disable-gpu')
chrome_options.add_argument('--disable-software-rasterizer')
chrome_options.add_argument('--disable-background-timer-throttling')
chrome_options.add_argument('--disable-backgrounding-occluded-windows')
chrome_options.add_argument('--disable-renderer-backgrounding')
chrome_options.add_argument('--disable-features=TranslateUI')
chrome_options.add_argument('--disable-ipc-flooding-protection')

# 性能优化
chrome_options.add_argument('--disable-extensions')
chrome_options.add_argument('--disable-plugins')
```

## 验证码处理

### ddddocr自动识别功能
本项目集成了ddddocr验证码自动识别库，具有以下特点：
- **高准确率**：专门针对验证码优化的识别算法
- **本地处理**：无需网络连接，保护隐私
- **多重重试**：识别失败时自动重试，最多3次
- **智能清理**：自动清理识别结果中的特殊字符
- **兼容性修补**：自动处理Pillow库版本兼容性问题

### 验证码处理流程
1. **截取验证码**：自动定位并截取验证码图片
2. **图像预处理**：增强对比度、去噪、二值化处理
3. **ddddocr识别**：使用深度学习模型自动识别
4. **结果验证**：检查识别结果的合理性
5. **自动重试**：如果识别失败或验证码错误，自动刷新重试
6. **手动备选**：所有自动方式失败后，提供手动输入选项

### 无头模式下的验证码处理
1. **自动识别**：优先使用ddddocr自动识别（成功率约85%）
2. **重试机制**：验证码错误时自动刷新并重新识别，最多4次尝试
3. **手动输入**：如果自动识别失败，会保存验证码图片到文件
4. **查看图片**：在文件管理器中查看保存的验证码图片
5. **输入验证码**：在命令行中输入识别的验证码

### 窗口模式下的验证码处理
1. **自动识别**：同样优先使用ddddocr自动识别
2. **直观查看**：可以直接在浏览器窗口中看到验证码
3. **实时监控**：可以观察识别过程和结果
4. **手动输入**：在命令行中输入验证码

### 验证码识别日志示例
```
2025-07-29 18:00:35,228 - INFO - 开始ddddocr识别验证码: captcha_attempt_1_xxx.png
2025-07-29 18:00:35,228 - INFO - ddddocr识别尝试 1/3
2025-07-29 18:00:35,247 - INFO - ddddocr识别结果 (尝试1): 原始='smx2', 清理后='SMX2'
2025-07-29 18:00:35,247 - INFO - ddddocr识别成功: 'SMX2'
```

## 推荐设置

### 开发阶段
```python
HEADLESS_MODE = False  # 使用窗口模式便于调试
```

### 生产环境
```python
HEADLESS_MODE = True   # 使用无头模式提高效率
```

### 首次使用
建议先使用窗口模式，确认流程正常后再切换到无头模式：

1. 设置 `HEADLESS_MODE = False`
2. 运行程序，观察整个登录流程
3. 确认验证码识别和登录功能正常
4. 设置 `HEADLESS_MODE = True`
5. 切换到无头模式进行正式使用

## 故障排除

### 无头模式常见问题

**问题1：验证码识别失败**
- 检查ddddocr是否正确安装
- 查看保存的验证码图片质量
- 考虑临时切换到窗口模式调试

**问题2：页面加载异常**
- 检查网络连接
- 增加等待时间
- 查看日志输出

**问题3：无法看到错误信息**
- 查看控制台日志输出
- 临时切换到窗口模式观察
- 检查保存的截图文件

### 切换模式测试
如果无头模式出现问题，可以快速切换到窗口模式进行对比测试：

```python
# 临时测试用窗口模式
login_bot = SeleniumLoginSolution(headless=False, ddddocr_enabled=True)
```

## 性能对比

| 项目 | 无头模式 | 窗口模式 |
|------|----------|----------|
| 内存占用 | 较低 | 较高 |
| CPU占用 | 较低 | 较高 |
| 启动速度 | 较快 | 较慢 |
| 运行速度 | 较快 | 较慢 |
| 调试便利性 | 较低 | 较高 |
| 服务器适用性 | 高 | 低 |

## ddddocr验证码识别库配置

### 安装ddddocr

```bash
pip install ddddocr
```

### 常见问题及解决方案

#### 问题1：Pillow兼容性错误
**错误信息：**
```
AttributeError: module 'PIL.Image' has no attribute 'ANTIALIAS'
```

**原因：** 新版Pillow库（11.x）将 `Image.ANTIALIAS` 重命名为 `Image.LANCZOS`，但ddddocr还在使用旧的API名称。

**解决方案：**

程序已内置自动修补功能，会在导入ddddocr前自动修复兼容性问题：

```python
# 修补Pillow兼容性问题
def patch_pillow_compatibility():
    """修补Pillow兼容性问题"""
    try:
        from PIL import Image
        if not hasattr(Image, 'ANTIALIAS'):
            Image.ANTIALIAS = Image.LANCZOS
            print("[+] 已修补Pillow兼容性问题")
    except:
        pass
```

如果仍有问题，可以手动降级Pillow版本：
```bash
pip install Pillow==9.5.0
```

#### 问题2：ddddocr版本兼容性
**Python版本对应关系：**
- **Python 3.13**：只能使用 `ddddocr==1.0.6`
- **Python 3.12**：可以使用 `ddddocr==1.4.11` 或更高版本
- **Python 3.11及以下**：可以使用最新版本

**解决方案：**
```bash
# 检查Python版本
python --version

# 根据Python版本安装对应的ddddocr版本
# Python 3.13用户（推荐）
pip install ddddocr==1.0.6

# Python 3.12及以下用户
pip install ddddocr==1.4.11
```

#### 问题3：模型下载失败
**现象：** 首次使用时下载模型文件失败

**解决方案：**
1. 检查网络连接
2. 使用代理（如果需要）
3. 手动下载模型文件

#### 问题4：识别准确率低
**优化方案：**
1. 图像预处理（程序已内置）
2. 多次重试机制（程序已内置）
3. 结合手动输入作为备选

### ddddocr功能特点

#### 优势
- **免费使用**：完全免费，无需API密钥
- **本地识别**：不需要网络连接
- **识别准确**：专门针对验证码优化
- **开箱即用**：安装后直接使用

#### 支持的验证码类型
- 数字验证码
- 字母验证码
- 数字+字母混合验证码
- 简单的图形验证码

### 验证码识别流程

程序中的验证码识别流程：

1. **截取验证码图片**
2. **图像预处理**（增强对比度、去噪等）
3. **ddddocr识别**（最多重试3次）
4. **结果清理**（去除特殊字符、转大写）
5. **识别失败回退**（手动输入）

### 手动测试ddddocr

如果需要单独测试ddddocr功能，可以使用以下代码：

```python
# 修补兼容性
from PIL import Image
if not hasattr(Image, 'ANTIALIAS'):
    Image.ANTIALIAS = Image.LANCZOS

# 导入ddddocr
import ddddocr

# 初始化识别器
ocr = ddddocr.DdddOcr()

# 读取验证码图片
with open('captcha.png', 'rb') as f:
    img_bytes = f.read()

# 识别验证码
result = ocr.classification(img_bytes)
print(f"识别结果: {result}")
```

### 依赖库说明

ddddocr的主要依赖：
- **Pillow**：图像处理
- **numpy**：数值计算
- **onnxruntime**：模型推理
- **opencv-python**：图像处理

这些依赖会在安装ddddocr时自动安装。

## 总结

- **默认使用无头模式**，适合大多数自动化场景
- **需要调试时切换到窗口模式**
- **生产环境建议使用无头模式**
- **首次使用建议先用窗口模式测试**
- **ddddocr提供强大的验证码自动识别能力**
- **程序已内置兼容性修补和错误处理机制**
